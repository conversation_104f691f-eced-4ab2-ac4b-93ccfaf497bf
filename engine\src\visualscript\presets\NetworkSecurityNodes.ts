/**
 * 视觉脚本网络安全节点
 * 提供数据加密/解密、用户认证、权限验证等功能
 */
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { NetworkSecuritySystem, EncryptionAlgorithm, HashAlgorithm } from '../../network/NetworkSecuritySystem';

/**
 * 数据加密节点
 * 加密数据
 */
export class EncryptDataNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要加密的数据'
    });

    this.addInput({
      name: 'algorithm',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密算法',
      defaultValue: 'AES'
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密密钥'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'encryptedData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '加密后的数据'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const data = this.getInputValue('data');
    const algorithm = this.getInputValue('algorithm') as EncryptionAlgorithm;
    const key = this.getInputValue('key') as string;

    // 检查输入值是否有效
    if (data === undefined || !algorithm || !key) {
      this.setOutputValue('encryptedData', null);
      this.triggerFlow('flow');
      return null;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('encryptedData', null);
      this.triggerFlow('flow');
      return null;
    }

    try {
      // 加密数据
      const encryptedData = securitySystem.encryptData(data, algorithm, key);

      // 设置输出值
      this.setOutputValue('encryptedData', encryptedData);

      // 触发输出流程
      this.triggerFlow('flow');

      return encryptedData;
    } catch (error) {
      console.error('加密数据失败:', error);
      this.setOutputValue('encryptedData', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 数据解密节点
 * 解密数据
 */
export class DecryptDataNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'encryptedData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要解密的数据'
    });

    this.addInput({
      name: 'algorithm',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密算法',
      defaultValue: 'AES'
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密密钥'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'decryptedData',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '解密后的数据'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const encryptedData = this.getInputValue('encryptedData') as string;
    const algorithm = this.getInputValue('algorithm') as EncryptionAlgorithm;
    const key = this.getInputValue('key') as string;

    // 检查输入值是否有效
    if (!encryptedData || !algorithm || !key) {
      this.setOutputValue('decryptedData', null);
      this.triggerFlow('flow');
      return null;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('decryptedData', null);
      this.triggerFlow('flow');
      return null;
    }

    try {
      // 解密数据
      const decryptedData = securitySystem.decryptData(encryptedData, algorithm, key);

      // 设置输出值
      this.setOutputValue('decryptedData', decryptedData);

      // 触发输出流程
      this.triggerFlow('flow');

      return decryptedData;
    } catch (error) {
      console.error('解密数据失败:', error);
      this.setOutputValue('decryptedData', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 用户认证节点
 * 验证用户身份
 */
export class UserAuthenticationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'userId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户ID'
    });

    this.addInput({
      name: 'credentials',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '认证凭据'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '认证成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '认证失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'user',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '用户信息'
    });

    this.addOutput({
      name: 'token',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '认证令牌'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const userId = this.getInputValue('userId') as string;
    const credentials = this.getInputValue('credentials') as object;

    // 检查输入值是否有效
    if (!userId || !credentials) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 模拟用户认证（实际应用中应该连接到真实的认证服务）
      // 这里简单检查凭据是否包含必要的字段
      const creds = credentials as any;
      if (creds && (creds.password || creds.token || creds.apiKey)) {
        // 创建会话
        const sessionId = securitySystem.createSession(userId, { authenticated: true });

        // 模拟用户信息
        const user = {
          id: userId,
          name: `User_${userId}`,
          authenticated: true,
          sessionId
        };

        // 设置输出值
        this.setOutputValue('user', user);
        this.setOutputValue('token', sessionId);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('用户认证失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 计算哈希节点
 * 计算数据的哈希值
 */
export class ComputeHashNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要计算哈希的数据'
    });

    this.addInput({
      name: 'algorithm',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '哈希算法',
      defaultValue: HashAlgorithm.SHA256
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'hash',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '哈希值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const data = this.getInputValue('data');
    const algorithm = this.getInputValue('algorithm') as HashAlgorithm;

    // 检查输入值是否有效
    if (data === undefined || !algorithm) {
      this.setOutputValue('hash', null);
      this.triggerFlow('flow');
      return null;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('hash', null);
      this.triggerFlow('flow');
      return null;
    }

    try {
      // 计算哈希
      const hash = securitySystem.computeHash(data, algorithm);

      // 设置输出值
      this.setOutputValue('hash', hash);

      // 触发输出流程
      this.triggerFlow('flow');

      return hash;
    } catch (error) {
      console.error('计算哈希失败:', error);
      this.setOutputValue('hash', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 生成签名节点
 * 生成数据签名
 */
export class GenerateSignatureNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要签名的数据'
    });

    this.addInput({
      name: 'privateKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '私钥'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'signature',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '签名'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const data = this.getInputValue('data');
    const privateKey = this.getInputValue('privateKey') as string;

    // 检查输入值是否有效
    if (data === undefined || !privateKey) {
      this.setOutputValue('signature', null);
      this.triggerFlow('flow');
      return null;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('signature', null);
      this.triggerFlow('flow');
      return null;
    }

    try {
      // 生成签名
      const signature = securitySystem.generateSignature(data, privateKey);

      // 设置输出值
      this.setOutputValue('signature', signature);

      // 触发输出流程
      this.triggerFlow('flow');

      return signature;
    } catch (error) {
      console.error('生成签名失败:', error);
      this.setOutputValue('signature', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 验证签名节点
 * 验证数据签名
 */
export class VerifySignatureNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '签名的数据'
    });

    this.addInput({
      name: 'signature',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '签名'
    });

    this.addInput({
      name: 'publicKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '公钥'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'valid',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '签名有效'
    });

    this.addOutput({
      name: 'invalid',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '签名无效'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'isValid',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否有效'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const data = this.getInputValue('data');
    const signature = this.getInputValue('signature') as string;
    const publicKey = this.getInputValue('publicKey') as string;

    // 检查输入值是否有效
    if (data === undefined || !signature || !publicKey) {
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }

    try {
      // 验证签名
      const isValid = securitySystem.verifySignature(data, signature, publicKey);

      // 设置输出值
      this.setOutputValue('isValid', isValid);

      // 触发输出流程
      if (isValid) {
        this.triggerFlow('valid');
      } else {
        this.triggerFlow('invalid');
      }

      return isValid;
    } catch (error) {
      console.error('验证签名失败:', error);
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }
  }
}

/**
 * 创建会话节点
 * 创建用户会话
 */
export class CreateSessionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'userId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户ID'
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '会话数据',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '会话ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const userId = this.getInputValue('userId') as string;
    const data = this.getInputValue('data') as object;

    // 检查输入值是否有效
    if (!userId) {
      this.setOutputValue('sessionId', null);
      this.triggerFlow('flow');
      return null;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('sessionId', null);
      this.triggerFlow('flow');
      return null;
    }

    try {
      // 创建会话
      const sessionId = securitySystem.createSession(userId, data);

      // 设置输出值
      this.setOutputValue('sessionId', sessionId);

      // 触发输出流程
      this.triggerFlow('flow');

      return sessionId;
    } catch (error) {
      console.error('创建会话失败:', error);
      this.setOutputValue('sessionId', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 验证会话节点
 * 验证用户会话
 */
export class ValidateSessionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'valid',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '会话有效'
    });

    this.addOutput({
      name: 'invalid',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '会话无效'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'isValid',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否有效'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const sessionId = this.getInputValue('sessionId') as string;

    // 检查输入值是否有效
    if (!sessionId) {
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }

    // 获取网络安全系统
    const securitySystem = this.context.world.getSystem(NetworkSecuritySystem);
    if (!securitySystem) {
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }

    try {
      // 验证会话
      const isValid = securitySystem.validateSession(sessionId);

      // 设置输出值
      this.setOutputValue('isValid', isValid);

      // 触发输出流程
      if (isValid) {
        this.triggerFlow('valid');
      } else {
        this.triggerFlow('invalid');
      }

      return isValid;
    } catch (error) {
      console.error('验证会话失败:', error);
      this.setOutputValue('isValid', false);
      this.triggerFlow('invalid');
      return false;
    }
  }
}

/**
 * 注册网络安全节点
 * @param registry 节点注册表
 */
export function registerNetworkSecurityNodes(registry: NodeRegistry): void {
  // 注册数据加密节点
  registry.registerNodeType({
    type: 'network/security/encryptData',
    category: NodeCategory.NETWORK,
    constructor: EncryptDataNode,
    label: '数据加密',
    description: '加密数据',
    icon: 'encrypt',
    color: '#00BCD4',
    tags: ['network', 'security', 'encrypt']
  });

  // 注册数据解密节点
  registry.registerNodeType({
    type: 'network/security/decryptData',
    category: NodeCategory.NETWORK,
    constructor: DecryptDataNode,
    label: '数据解密',
    description: '解密数据',
    icon: 'decrypt',
    color: '#00BCD4',
    tags: ['network', 'security', 'decrypt']
  });

  // 注册计算哈希节点
  registry.registerNodeType({
    type: 'network/security/computeHash',
    category: NodeCategory.NETWORK,
    constructor: ComputeHashNode,
    label: '计算哈希',
    description: '计算数据的哈希值',
    icon: 'hash',
    color: '#00BCD4',
    tags: ['network', 'security', 'hash']
  });

  // 注册生成签名节点
  registry.registerNodeType({
    type: 'network/security/generateSignature',
    category: NodeCategory.NETWORK,
    constructor: GenerateSignatureNode,
    label: '生成签名',
    description: '生成数据签名',
    icon: 'signature',
    color: '#00BCD4',
    tags: ['network', 'security', 'signature', 'sign']
  });

  // 注册验证签名节点
  registry.registerNodeType({
    type: 'network/security/verifySignature',
    category: NodeCategory.NETWORK,
    constructor: VerifySignatureNode,
    label: '验证签名',
    description: '验证数据签名',
    icon: 'verify',
    color: '#00BCD4',
    tags: ['network', 'security', 'signature', 'verify']
  });

  // 注册创建会话节点
  registry.registerNodeType({
    type: 'network/security/createSession',
    category: NodeCategory.NETWORK,
    constructor: CreateSessionNode,
    label: '创建会话',
    description: '创建用户会话',
    icon: 'session',
    color: '#00BCD4',
    tags: ['network', 'security', 'session', 'create']
  });

  // 注册验证会话节点
  registry.registerNodeType({
    type: 'network/security/validateSession',
    category: NodeCategory.NETWORK,
    constructor: ValidateSessionNode,
    label: '验证会话',
    description: '验证用户会话',
    icon: 'validate',
    color: '#00BCD4',
    tags: ['network', 'security', 'session', 'validate']
  });

  // 注册用户认证节点
  registry.registerNodeType({
    type: 'network/security/authenticateUser',
    category: NodeCategory.NETWORK,
    constructor: UserAuthenticationNode,
    label: '用户认证',
    description: '验证用户身份',
    icon: 'authenticate',
    color: '#00BCD4',
    tags: ['network', 'security', 'authentication', 'user']
  });
}
